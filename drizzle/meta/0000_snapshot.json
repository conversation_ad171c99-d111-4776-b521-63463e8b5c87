{"id": "94b6c4a9-2551-4a8e-afc3-54774de2a123", "prevId": "********-0000-0000-0000-********0000", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.profiles": {"name": "profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"profiles_user_id_users_id_fk": {"name": "profiles_user_id_users_id_fk", "tableFrom": "profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"profiles_user_id_unique": {"name": "profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_token": {"name": "session_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_session_token_unique": {"name": "sessions_session_token_unique", "nullsNotDistinct": false, "columns": ["session_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "timestamp", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "hashed_password": {"name": "hashed_password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification_tokens": {"name": "verification_tokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"verification_tokens_token_unique": {"name": "verification_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.youtube_analytics": {"name": "youtube_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "channel_id": {"name": "channel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "video_id": {"name": "video_id", "type": "uuid", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "watch_time": {"name": "watch_time", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "subscribers": {"name": "subscribers", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "comments": {"name": "comments", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "shares": {"name": "shares", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "impressions": {"name": "impressions", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "click_through_rate": {"name": "click_through_rate", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "average_view_duration": {"name": "average_view_duration", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"youtube_analytics_channel_id_youtube_channels_id_fk": {"name": "youtube_analytics_channel_id_youtube_channels_id_fk", "tableFrom": "youtube_analytics", "tableTo": "youtube_channels", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "youtube_analytics_video_id_youtube_videos_id_fk": {"name": "youtube_analytics_video_id_youtube_videos_id_fk", "tableFrom": "youtube_analytics", "tableTo": "youtube_videos", "columnsFrom": ["video_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.youtube_channels": {"name": "youtube_channels", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "subscriber_count": {"name": "subscriber_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "video_count": {"name": "video_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "token_expires_at": {"name": "token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"youtube_channels_user_id_users_id_fk": {"name": "youtube_channels_user_id_users_id_fk", "tableFrom": "youtube_channels", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"youtube_channels_channel_id_unique": {"name": "youtube_channels_channel_id_unique", "nullsNotDistinct": false, "columns": ["channel_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.youtube_videos": {"name": "youtube_videos", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "channel_id": {"name": "channel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "video_id": {"name": "video_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "like_count": {"name": "like_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "comment_count": {"name": "comment_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"youtube_videos_channel_id_youtube_channels_id_fk": {"name": "youtube_videos_channel_id_youtube_channels_id_fk", "tableFrom": "youtube_videos", "tableTo": "youtube_channels", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"youtube_videos_video_id_unique": {"name": "youtube_videos_video_id_unique", "nullsNotDistinct": false, "columns": ["video_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}