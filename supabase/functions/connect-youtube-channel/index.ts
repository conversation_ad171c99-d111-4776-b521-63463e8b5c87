
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { user_id } = await req.json()
    
    if (!user_id) {
      throw new Error('User ID is required')
    }

    // For now, create a mock channel entry to demonstrate the flow
    // In a real implementation, this would:
    // 1. Redirect user to YouTube OAuth
    // 2. Exchange code for access token
    // 3. Fetch channel data from YouTube API
    // 4. Store channel data in database
    
    const mockChannelData = {
      id: `UC${Math.random().toString(36).substring(2, 15)}`,
      user_id: user_id,
      title: "Demo YouTube Channel",
      description: "This is a demo channel for testing purposes",
      thumbnail_url: "https://via.placeholder.com/88x88?text=YT",
      subscriber_count: Math.floor(Math.random() * 100000),
      view_count: Math.floor(Math.random() * 1000000),
      video_count: Math.floor(Math.random() * 500),
    }

    const { data, error } = await supabaseClient
      .from('youtube_channels')
      .insert([mockChannelData])
      .select()

    if (error) {
      console.error('Database error:', error)
      throw error
    }

    console.log('Channel connected successfully:', data)

    return new Response(
      JSON.stringify({ success: true, channel: data[0] }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error in connect-youtube-channel:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
      }
    )
  }
})
