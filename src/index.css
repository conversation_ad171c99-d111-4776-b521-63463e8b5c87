@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Professional Dark Theme as Default */
    --background: 222 47% 4%;
    --foreground: 213 31% 91%;

    --card: 222 47% 6%;
    --card-foreground: 213 31% 91%;

    --popover: 222 47% 6%;
    --popover-foreground: 213 31% 91%;

    --primary: 217 91% 60%;
    --primary-foreground: 222 47% 4%;

    --secondary: 222 47% 8%;
    --secondary-foreground: 213 31% 91%;

    --muted: 222 47% 8%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 91% 60%;
    --accent-foreground: 222 47% 4%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 222 47% 12%;
    --input: 222 47% 12%;
    --ring: 217 91% 60%;

    --radius: 0.75rem;

    --sidebar-background: 222 47% 4%;
    --sidebar-foreground: 213 31% 91%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 222 47% 4%;
    --sidebar-accent: 222 47% 8%;
    --sidebar-accent-foreground: 213 31% 91%;
    --sidebar-border: 222 47% 12%;
    --sidebar-ring: 217 91% 60%;
  }

  .light {
    /* Light theme for toggle option */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 60%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .gradient-bg {
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
  }

  .glass-effect {
    backdrop-filter: blur(20px);
    background: hsl(var(--background) / 0.8);
    border: 1px solid hsl(var(--border) / 0.5);
  }

  .professional-shadow {
    box-shadow: 0 4px 6px -1px hsl(var(--foreground) / 0.1), 0 2px 4px -1px hsl(var(--foreground) / 0.06);
  }

  .professional-shadow-lg {
    box-shadow: 0 10px 15px -3px hsl(var(--foreground) / 0.1), 0 4px 6px -2px hsl(var(--foreground) / 0.05);
  }
}