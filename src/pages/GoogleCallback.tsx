import React, { useEffect, useState } from 'react';
import { Navigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const GoogleCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { user, loading } = useAuth();
  const [processing, setProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const error = searchParams.get('error');

        if (error) {
          setError(`OAuth error: ${error}`);
          setProcessing(false);
          return;
        }

        if (!code) {
          setError('No authorization code received');
          setProcessing(false);
          return;
        }

        // Call the global callback handler
        const handleGoogleCallback = (window as any).handleGoogleCallback;
        if (handleGoogleCallback) {
          const result = await handleGoogleCallback(code);
          if (result.error) {
            setError(result.error.message);
          }
        } else {
          setError('Authentication handler not available');
        }
      } catch (err: any) {
        setError(err.message || 'Failed to process authentication');
      } finally {
        setProcessing(false);
      }
    };

    handleCallback();
  }, [searchParams]);

  // Show loading while processing
  if (processing || loading) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-foreground text-lg">Completing sign in...</p>
        </div>
      </div>
    );
  }

  // Show error if something went wrong
  if (error) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-foreground mb-2">Authentication Failed</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <a 
              href="/auth" 
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Try Again
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Redirect to dashboard if authenticated
  if (user) {
    return <Navigate to="/dashboard" replace />;
  }

  // Fallback redirect to auth page
  return <Navigate to="/auth" replace />;
};

export default GoogleCallback;
