
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { Youtube, Users, Eye, Clock, LogOut, BarChart3 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import YouTubeIntegration from '@/components/YouTubeIntegration';
import ThemeToggle from '@/components/ThemeToggle';

const Dashboard = () => {
  const { user, signOut } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');

  const handleSignOut = async () => {
    await signOut();
    toast({
      title: "Signed out",
      description: "You've been successfully signed out."
    });
  };

  return (
    <div className="min-h-screen gradient-bg">
      {/* Header */}
      <header className="glass-effect border-b border-border/50 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-primary p-2.5 rounded-xl professional-shadow">
              <Youtube className="h-6 w-6 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold text-foreground tracking-tight">ChannelScope</h1>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-muted-foreground hidden sm:block">
              Welcome, {user?.user_metadata?.full_name || user?.email}
            </span>
            <ThemeToggle />
            <Button variant="outline" size="sm" onClick={handleSignOut}>
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 lg:w-auto">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="youtube" className="flex items-center space-x-2">
              <Youtube className="h-4 w-4" />
              <span>YouTube</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            <div className="mb-10">
              <h2 className="text-4xl font-bold text-foreground mb-3">Dashboard Overview</h2>
              <p className="text-xl text-muted-foreground">
                Track your YouTube performance across all connected channels
              </p>
            </div>

            {/* Quick Stats */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:professional-shadow transition-all duration-300">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-foreground">Connected Channels</CardTitle>
                  <Youtube className="h-5 w-5 text-primary" />
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-foreground">0</div>
                  <p className="text-xs text-muted-foreground">
                    +0 from last month
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:professional-shadow transition-all duration-300">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-foreground">Total Subscribers</CardTitle>
                  <Users className="h-5 w-5 text-primary" />
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-foreground">-</div>
                  <p className="text-xs text-muted-foreground">
                    Connect a channel to see data
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:professional-shadow transition-all duration-300">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-foreground">Total Views</CardTitle>
                  <Eye className="h-5 w-5 text-primary" />
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-foreground">-</div>
                  <p className="text-xs text-muted-foreground">
                    Connect a channel to see data
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:professional-shadow transition-all duration-300">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-foreground">Watch Time</CardTitle>
                  <Clock className="h-5 w-5 text-primary" />
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-foreground">-</div>
                  <p className="text-xs text-muted-foreground">
                    Connect a channel to see data
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Getting Started */}
            <Card className="bg-card/50 backdrop-blur-sm border-border/50 professional-shadow">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-foreground">Get Started with ChannelScope</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Follow these steps to start tracking your YouTube analytics
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-primary text-primary-foreground rounded-full w-10 h-10 flex items-center justify-center text-sm font-bold professional-shadow">
                    1
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-foreground text-lg">Connect Your YouTube Channel</h4>
                    <p className="text-muted-foreground mt-1">
                      Link your YouTube channel to start collecting analytics data
                    </p>
                    <Button
                      className="mt-3 professional-shadow"
                      onClick={() => setActiveTab('youtube')}
                    >
                      Connect Channel
                    </Button>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-muted text-muted-foreground rounded-full w-10 h-10 flex items-center justify-center text-sm font-bold">
                    2
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-muted-foreground text-lg">View Your Analytics</h4>
                    <p className="text-muted-foreground/70 mt-1">
                      Monitor your channel's performance with detailed insights
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-muted text-muted-foreground rounded-full w-10 h-10 flex items-center justify-center text-sm font-bold">
                    3
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-muted-foreground text-lg">Upgrade Your Plan</h4>
                    <p className="text-muted-foreground/70 mt-1">
                      Connect more channels and unlock advanced features
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="youtube">
            <YouTubeIntegration />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default Dashboard;
