
import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Youtube, Users, Eye, Clock, ArrowUp, ArrowDown, Play, ThumbsUp, MessageCircle, Calendar } from 'lucide-react';
import { Link } from 'react-router-dom';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, ResponsiveContainer } from 'recharts';

const Demo = () => {
  // Sample data for charts
  const subscriberGrowthData = [
    { month: 'Jan', subscribers: 12500, views: 450000 },
    { month: 'Feb', subscribers: 13200, views: 520000 },
    { month: 'Mar', subscribers: 14100, views: 610000 },
    { month: 'Apr', subscribers: 15800, views: 720000 },
    { month: 'May', subscribers: 17200, views: 850000 },
    { month: 'Jun', subscribers: 18900, views: 950000 },
  ];

  const videoPerformanceData = [
    { day: 'Mon', views: 12000, engagement: 8.5 },
    { day: 'Tue', views: 15000, engagement: 9.2 },
    { day: 'Wed', views: 18000, engagement: 10.1 },
    { day: 'Thu', views: 22000, engagement: 11.5 },
    { day: 'Fri', views: 28000, engagement: 12.8 },
    { day: 'Sat', views: 32000, engagement: 14.2 },
    { day: 'Sun', views: 35000, engagement: 15.1 },
  ];

  const topVideos = [
    { title: "How to Build a Successful YouTube Channel", views: 125000, likes: 8500, comments: 420, published: "2 days ago" },
    { title: "Top 10 Content Creation Tips", views: 98000, likes: 6200, comments: 310, published: "1 week ago" },
    { title: "YouTube Analytics Explained", views: 87000, likes: 5800, comments: 280, published: "2 weeks ago" },
    { title: "Monetization Strategies for Creators", views: 76000, likes: 4900, comments: 250, published: "3 weeks ago" },
    { title: "Video Editing Masterclass", views: 65000, likes: 4100, comments: 180, published: "1 month ago" },
  ];

  const channels = [
    { name: "Tech Reviews", subscribers: 18900, growth: 12.5, color: "bg-blue-500" },
    { name: "Gaming Corner", subscribers: 15200, growth: 8.3, color: "bg-green-500" },
    { name: "Lifestyle Vlogs", subscribers: 12800, growth: -2.1, color: "bg-purple-500" },
  ];

  const chartConfig = {
    subscribers: { label: "Subscribers", color: "#ef4444" },
    views: { label: "Views", color: "#3b82f6" },
    engagement: { label: "Engagement %", color: "#10b981" }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="bg-red-500 p-2 rounded-lg">
              <Youtube className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-slate-900">ChannelScope</h1>
            <Badge variant="secondary" className="ml-2">Demo</Badge>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link to="/">← Back to Home</Link>
            </Button>
            <Button asChild>
              <Link to="/auth">Start Free Trial</Link>
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-slate-900 mb-2">Demo Dashboard</h2>
          <p className="text-slate-600">
            Experience ChannelScope with sample data from 3 connected YouTube channels
          </p>
        </div>

        {/* Channel Overview Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {channels.map((channel, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{channel.name}</CardTitle>
                <div className={`w-3 h-3 rounded-full ${channel.color}`}></div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{channel.subscribers.toLocaleString()}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {channel.growth > 0 ? (
                    <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={channel.growth > 0 ? "text-green-500" : "text-red-500"}>
                    {Math.abs(channel.growth)}%
                  </span>
                  <span className="ml-1">vs last month</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">46,900</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-500">+8.2%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Views</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2.1M</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-500">+15.3%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Watch Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12,500h</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-500">+22.1%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Engagement</CardTitle>
              <ThumbsUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8.7%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-500">+1.2%</span> from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid lg:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Subscriber Growth</CardTitle>
              <CardDescription>Monthly subscriber growth across all channels</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-64">
                <LineChart data={subscriberGrowthData}>
                  <XAxis dataKey="month" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Line 
                    type="monotone" 
                    dataKey="subscribers" 
                    stroke="var(--color-subscribers)" 
                    strokeWidth={2} 
                  />
                </LineChart>
              </ChartContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Weekly Performance</CardTitle>
              <CardDescription>Views and engagement over the past week</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-64">
                <AreaChart data={videoPerformanceData}>
                  <XAxis dataKey="day" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Area 
                    type="monotone" 
                    dataKey="views" 
                    stroke="var(--color-views)" 
                    fill="var(--color-views)" 
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>

        {/* Top Performing Videos */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Top Performing Videos</CardTitle>
            <CardDescription>Your best content from the past month</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Video Title</TableHead>
                  <TableHead className="text-right">Views</TableHead>
                  <TableHead className="text-right">Likes</TableHead>
                  <TableHead className="text-right">Comments</TableHead>
                  <TableHead className="text-right">Published</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {topVideos.map((video, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Play className="h-4 w-4 text-red-500" />
                        <span className="font-medium">{video.title}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">{video.views.toLocaleString()}</TableCell>
                    <TableCell className="text-right">{video.likes.toLocaleString()}</TableCell>
                    <TableCell className="text-right">{video.comments}</TableCell>
                    <TableCell className="text-right text-muted-foreground">{video.published}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="bg-gradient-to-r from-red-500 to-red-600 text-white">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">Ready to Track Your Real Data?</h3>
            <p className="text-red-100 mb-6 text-lg">
              Connect your YouTube channels and start getting insights like these for your content
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-red-600 hover:bg-red-50" asChild>
                <Link to="/auth">Start Free Trial</Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                Learn More
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default Demo;
