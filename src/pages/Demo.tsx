
import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Youtube, Users, Eye, Clock, ArrowUp, ArrowDown, Play, ThumbsUp, MessageCircle, Calendar } from 'lucide-react';
import { Link } from 'react-router-dom';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, ResponsiveContainer } from 'recharts';
import ThemeToggle from '@/components/ThemeToggle';

const Demo = () => {
  // Sample data for charts
  const subscriberGrowthData = [
    { month: 'Jan', subscribers: 12500, views: 450000 },
    { month: 'Feb', subscribers: 13200, views: 520000 },
    { month: 'Mar', subscribers: 14100, views: 610000 },
    { month: 'Apr', subscribers: 15800, views: 720000 },
    { month: 'May', subscribers: 17200, views: 850000 },
    { month: 'Jun', subscribers: 18900, views: 950000 },
  ];

  const videoPerformanceData = [
    { day: 'Mon', views: 12000, engagement: 8.5 },
    { day: 'Tue', views: 15000, engagement: 9.2 },
    { day: 'Wed', views: 18000, engagement: 10.1 },
    { day: 'Thu', views: 22000, engagement: 11.5 },
    { day: 'Fri', views: 28000, engagement: 12.8 },
    { day: 'Sat', views: 32000, engagement: 14.2 },
    { day: 'Sun', views: 35000, engagement: 15.1 },
  ];

  const topVideos = [
    { title: "How to Build a Successful YouTube Channel", views: 125000, likes: 8500, comments: 420, published: "2 days ago" },
    { title: "Top 10 Content Creation Tips", views: 98000, likes: 6200, comments: 310, published: "1 week ago" },
    { title: "YouTube Analytics Explained", views: 87000, likes: 5800, comments: 280, published: "2 weeks ago" },
    { title: "Monetization Strategies for Creators", views: 76000, likes: 4900, comments: 250, published: "3 weeks ago" },
    { title: "Video Editing Masterclass", views: 65000, likes: 4100, comments: 180, published: "1 month ago" },
  ];

  const channels = [
    { name: "Tech Reviews", subscribers: 18900, growth: 12.5, color: "bg-blue-500" },
    { name: "Gaming Corner", subscribers: 15200, growth: 8.3, color: "bg-green-500" },
    { name: "Lifestyle Vlogs", subscribers: 12800, growth: -2.1, color: "bg-purple-500" },
  ];

  const chartConfig = {
    subscribers: { label: "Subscribers", color: "#ef4444" },
    views: { label: "Views", color: "#3b82f6" },
    engagement: { label: "Engagement %", color: "#10b981" }
  };

  return (
    <div className="min-h-screen gradient-bg">
      {/* Header */}
      <header className="glass-effect border-b border-border/50 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-primary p-2.5 rounded-xl professional-shadow">
              <Youtube className="h-6 w-6 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold text-foreground tracking-tight">ChannelScope</h1>
            <Badge variant="secondary" className="ml-2">Demo</Badge>
          </div>
          <div className="flex items-center space-x-3">
            <ThemeToggle />
            <Button variant="ghost" size="sm" asChild>
              <Link to="/">← Back to Home</Link>
            </Button>
            <Button size="sm" asChild>
              <Link to="/auth">Start Free Trial</Link>
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-10">
        <div className="mb-12">
          <h2 className="text-4xl font-bold text-foreground mb-3">Demo Dashboard</h2>
          <p className="text-xl text-muted-foreground">
            Experience ChannelScope with sample data from 3 connected YouTube channels
          </p>
        </div>

        {/* Channel Overview Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-10">
          {channels.map((channel, index) => (
            <Card key={index} className="bg-card/50 backdrop-blur-sm border-border/50 hover:professional-shadow-lg transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-foreground">{channel.name}</CardTitle>
                <div className={`w-3 h-3 rounded-full ${channel.color}`}></div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-foreground">{channel.subscribers.toLocaleString()}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {channel.growth > 0 ? (
                    <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={channel.growth > 0 ? "text-green-500" : "text-red-500"}>
                    {Math.abs(channel.growth)}%
                  </span>
                  <span className="ml-1">vs last month</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
          <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:professional-shadow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-foreground">Total Subscribers</CardTitle>
              <Users className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">46,900</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-500">+8.2%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:professional-shadow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-foreground">Total Views</CardTitle>
              <Eye className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">2.1M</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-500">+15.3%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:professional-shadow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-foreground">Watch Time</CardTitle>
              <Clock className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">12,500h</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-500">+22.1%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:professional-shadow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-foreground">Avg. Engagement</CardTitle>
              <ThumbsUp className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">8.7%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-500">+1.2%</span> from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid lg:grid-cols-2 gap-8 mb-10">
          <Card className="bg-card/50 backdrop-blur-sm border-border/50 professional-shadow">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-foreground">Subscriber Growth</CardTitle>
              <CardDescription className="text-muted-foreground">Monthly subscriber growth across all channels</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-64">
                <LineChart data={subscriberGrowthData}>
                  <XAxis dataKey="month" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Line
                    type="monotone"
                    dataKey="subscribers"
                    stroke="var(--color-subscribers)"
                    strokeWidth={2}
                  />
                </LineChart>
              </ChartContainer>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50 professional-shadow">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-foreground">Weekly Performance</CardTitle>
              <CardDescription className="text-muted-foreground">Views and engagement over the past week</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-64">
                <AreaChart data={videoPerformanceData}>
                  <XAxis dataKey="day" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Area
                    type="monotone"
                    dataKey="views"
                    stroke="var(--color-views)"
                    fill="var(--color-views)"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>

        {/* Top Performing Videos */}
        <Card className="mb-10 bg-card/50 backdrop-blur-sm border-border/50 professional-shadow">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-foreground">Top Performing Videos</CardTitle>
            <CardDescription className="text-muted-foreground">Your best content from the past month</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-foreground">Video Title</TableHead>
                  <TableHead className="text-right text-foreground">Views</TableHead>
                  <TableHead className="text-right text-foreground">Likes</TableHead>
                  <TableHead className="text-right text-foreground">Comments</TableHead>
                  <TableHead className="text-right text-foreground">Published</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {topVideos.map((video, index) => (
                  <TableRow key={index} className="hover:bg-muted/50">
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Play className="h-4 w-4 text-primary" />
                        <span className="font-medium text-foreground">{video.title}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right text-foreground">{video.views.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-foreground">{video.likes.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-foreground">{video.comments}</TableCell>
                    <TableCell className="text-right text-muted-foreground">{video.published}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="bg-gradient-to-br from-primary/10 to-accent/10 border-border/50 professional-shadow-lg">
          <CardContent className="p-10 text-center">
            <h3 className="text-3xl font-bold text-foreground mb-4">Ready to Track Your Real Data?</h3>
            <p className="text-muted-foreground mb-8 text-lg max-w-2xl mx-auto">
              Connect your YouTube channels and start getting insights like these for your content
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="text-lg px-10 py-6 h-auto professional-shadow" asChild>
                <Link to="/auth">Start Free Trial</Link>
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-10 py-6 h-auto" asChild>
                <Link to="/">Learn More</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default Demo;
