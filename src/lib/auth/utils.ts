import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { db, users, accounts, sessions } from '../db';
import { eq, and } from 'drizzle-orm';

const JWT_SECRET = import.meta.env.VITE_JWT_SECRET || 'your-jwt-secret-here';

export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  image?: string;
  emailVerified?: Date;
}

export interface AuthSession {
  user: AuthUser;
  expires: Date;
  sessionToken: string;
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Generate JWT token
export function generateToken(payload: any): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
}

// Verify JWT token
export function verifyToken(token: string): any {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

// Create user
export async function createUser(email: string, password: string, name?: string) {
  const hashedPassword = await hashPassword(password);
  
  const [user] = await db.insert(users).values({
    email,
    hashedPassword,
    name,
  }).returning();

  return user;
}

// Find user by email
export async function findUserByEmail(email: string) {
  const [user] = await db.select().from(users).where(eq(users.email, email));
  return user;
}

// Find user by id
export async function findUserById(id: string) {
  const [user] = await db.select().from(users).where(eq(users.id, id));
  return user;
}

// Authenticate user with email and password
export async function authenticateUser(email: string, password: string): Promise<AuthUser | null> {
  const user = await findUserByEmail(email);
  
  if (!user || !user.hashedPassword) {
    return null;
  }

  const isValid = await verifyPassword(password, user.hashedPassword);
  
  if (!isValid) {
    return null;
  }

  return {
    id: user.id,
    email: user.email,
    name: user.name || undefined,
    image: user.image || undefined,
    emailVerified: user.emailVerified || undefined,
  };
}

// Create session
export async function createSession(userId: string): Promise<AuthSession> {
  const sessionToken = generateToken({ userId });
  const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

  await db.insert(sessions).values({
    userId,
    sessionToken,
    expires,
  });

  const user = await findUserById(userId);
  
  if (!user) {
    throw new Error('User not found');
  }

  return {
    user: {
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      image: user.image || undefined,
      emailVerified: user.emailVerified || undefined,
    },
    expires,
    sessionToken,
  };
}

// Get session by token
export async function getSessionByToken(sessionToken: string): Promise<AuthSession | null> {
  const [session] = await db
    .select({
      session: sessions,
      user: users,
    })
    .from(sessions)
    .innerJoin(users, eq(sessions.userId, users.id))
    .where(eq(sessions.sessionToken, sessionToken));

  if (!session || session.session.expires < new Date()) {
    return null;
  }

  return {
    user: {
      id: session.user.id,
      email: session.user.email,
      name: session.user.name || undefined,
      image: session.user.image || undefined,
      emailVerified: session.user.emailVerified || undefined,
    },
    expires: session.session.expires,
    sessionToken: session.session.sessionToken,
  };
}

// Delete session
export async function deleteSession(sessionToken: string): Promise<void> {
  await db.delete(sessions).where(eq(sessions.sessionToken, sessionToken));
}

// Create or update OAuth account
export async function createOrUpdateOAuthAccount(
  userId: string,
  provider: string,
  providerAccountId: string,
  accessToken?: string,
  refreshToken?: string,
  expiresAt?: number
) {
  const existingAccount = await db
    .select()
    .from(accounts)
    .where(
      and(
        eq(accounts.provider, provider),
        eq(accounts.providerAccountId, providerAccountId)
      )
    );

  if (existingAccount.length > 0) {
    // Update existing account
    await db
      .update(accounts)
      .set({
        accessToken,
        refreshToken,
        expiresAt,
        updatedAt: new Date(),
      })
      .where(eq(accounts.id, existingAccount[0].id));
  } else {
    // Create new account
    await db.insert(accounts).values({
      userId,
      type: 'oauth',
      provider,
      providerAccountId,
      accessToken,
      refreshToken,
      expiresAt,
    });
  }
}

// Find user by OAuth account
export async function findUserByOAuthAccount(provider: string, providerAccountId: string) {
  const [result] = await db
    .select({
      user: users,
      account: accounts,
    })
    .from(accounts)
    .innerJoin(users, eq(accounts.userId, users.id))
    .where(
      and(
        eq(accounts.provider, provider),
        eq(accounts.providerAccountId, providerAccountId)
      )
    );

  return result?.user;
}
