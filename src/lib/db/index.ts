import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import * as schema from './schema';

// Get the database URL from environment variables
const databaseUrl = import.meta.env.VITE_DATABASE_URL || 'postgresql://tubescope_owner:<EMAIL>/tubescope?sslmode=require';

if (!databaseUrl) {
  throw new Error('DATABASE_URL environment variable is not set');
}

// Create the connection
const sql = neon(databaseUrl);

// Create the database instance
export const db = drizzle(sql, { schema });

export * from './schema';
