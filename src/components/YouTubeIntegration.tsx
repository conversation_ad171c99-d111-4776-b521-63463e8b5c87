
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Youtube, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface YouTubeChannel {
  id: string;
  title: string;
  description: string | null;
  thumbnail_url: string | null;
  subscriber_count: number | null;
  view_count: number | null;
  video_count: number | null;
  connected_at: string;
}

const YouTubeIntegration = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isConnecting, setIsConnecting] = useState(false);
  const [channels, setChannels] = useState<YouTubeChannel[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchConnectedChannels();
    }
  }, [user]);

  const fetchConnectedChannels = async () => {
    try {
      const { data, error } = await supabase
        .from('youtube_channels')
        .select('*')
        .eq('user_id', user?.id);

      if (error) throw error;
      setChannels(data || []);
    } catch (error) {
      console.error('Error fetching channels:', error);
      toast({
        title: "Error",
        description: "Failed to fetch connected channels.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const connectYouTubeChannel = async () => {
    setIsConnecting(true);
    try {
      // Call edge function to handle YouTube OAuth and channel connection
      const { data, error } = await supabase.functions.invoke('connect-youtube-channel', {
        body: { user_id: user?.id }
      });

      if (error) throw error;

      toast({
        title: "Channel Connected!",
        description: "Your YouTube channel has been successfully connected."
      });

      await fetchConnectedChannels();
    } catch (error: any) {
      console.error('YouTube connection error:', error);
      toast({
        title: "Connection Failed",
        description: error.message || "Failed to connect YouTube channel. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const refreshChannelData = async (channelId: string) => {
    try {
      const { data, error } = await supabase.functions.invoke('refresh-youtube-data', {
        body: { channel_id: channelId, user_id: user?.id }
      });

      if (error) throw error;

      toast({
        title: "Data Refreshed",
        description: "Channel analytics have been updated."
      });

      await fetchConnectedChannels();
    } catch (error: any) {
      console.error('Refresh error:', error);
      toast({
        title: "Refresh Failed",
        description: error.message || "Failed to refresh channel data.",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">YouTube Integration</h2>
          <p className="text-slate-600">Connect and manage your YouTube channels</p>
        </div>
        <Button 
          onClick={connectYouTubeChannel} 
          disabled={isConnecting}
          className="bg-red-500 hover:bg-red-600"
        >
          <Youtube className="h-4 w-4 mr-2" />
          {isConnecting ? "Connecting..." : "Connect New Channel"}
        </Button>
      </div>

      {channels.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Youtube className="h-5 w-5 text-red-500" />
              <span>No Channels Connected</span>
            </CardTitle>
            <CardDescription>
              Connect your first YouTube channel to start tracking analytics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-slate-600 mb-4">
              Once connected, you'll be able to view real-time analytics including:
            </p>
            <ul className="list-disc list-inside text-sm text-slate-600 space-y-1">
              <li>Subscriber count and growth</li>
              <li>View counts and watch time</li>
              <li>Video performance metrics</li>
              <li>Revenue and engagement data</li>
            </ul>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {channels.map((channel) => (
            <Card key={channel.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <img 
                      src={channel.thumbnail_url || '/placeholder.svg'} 
                      alt={channel.title}
                      className="w-12 h-12 rounded-full"
                    />
                    <div>
                      <CardTitle className="flex items-center space-x-2">
                        <span>{channel.title}</span>
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      </CardTitle>
                      <CardDescription>{channel.description}</CardDescription>
                    </div>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => refreshChannelData(channel.id)}
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-red-500">
                      {(channel.subscriber_count || 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-slate-600">Subscribers</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-500">
                      {(channel.view_count || 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-slate-600">Total Views</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-500">
                      {(channel.video_count || 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-slate-600">Videos</div>
                  </div>
                </div>
                <div className="mt-4 text-xs text-slate-500">
                  Connected: {new Date(channel.connected_at).toLocaleDateString()}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default YouTubeIntegration;
