
import { But<PERSON> } from "@/components/ui/button";
import { Youtube, LogOut } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";

const Header = () => {
  const { user, signOut, loading } = useAuth();
  const { toast } = useToast();

  const handleSignOut = async () => {
    await signOut();
    toast({
      title: "Signed out",
      description: "You've been successfully signed out."
    });
  };

  return (
    <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="bg-red-500 p-2 rounded-lg">
            <Youtube className="h-6 w-6 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-slate-900">ChannelScope</h1>
        </div>
        <div className="flex items-center space-x-4">
          {loading ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
          ) : user ? (
            <>
              <span className="text-sm text-slate-600">
                {user.user_metadata?.full_name || user.email}
              </span>
              <Button asChild>
                <Link to="/dashboard">Dashboard</Link>
              </Button>
              <Button variant="outline" onClick={handleSignOut}>
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </>
          ) : (
            <>
              <Button variant="ghost" asChild>
                <Link to="/auth">Sign In</Link>
              </Button>
              <Button asChild>
                <Link to="/auth">Get Started</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
