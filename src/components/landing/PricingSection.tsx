
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowUp } from "lucide-react";
import { Link } from "react-router-dom";

const PricingSection = () => {
  const plans = [
    {
      name: "Free",
      price: "$0",
      description: "Perfect for getting started",
      features: [
        "1 YouTube channel",
        "Basic analytics",
        "30-day data retention",
        "Manual refresh"
      ],
      buttonText: "Get Started",
      buttonVariant: "outline" as const,
      popular: false
    },
    {
      name: "Pro",
      price: "$19",
      description: "For serious creators",
      features: [
        "5 YouTube channels",
        "Advanced analytics + trends",
        "12-month data retention",
        "Daily auto-refresh",
        "Email reports"
      ],
      buttonText: "Start Pro Trial",
      buttonVariant: "default" as const,
      popular: true
    },
    {
      name: "Premium",
      price: "$49",
      description: "For agencies & power users",
      features: [
        "10 YouTube channels",
        "All analytics + insights",
        "Unlimited data retention",
        "Real-time refresh",
        "Priority support",
        "API access"
      ],
      buttonText: "Contact Sales",
      buttonVariant: "outline" as const,
      popular: false
    }
  ];

  return (
    <section className="container mx-auto px-4 py-16">
      <div className="text-center mb-12">
        <h3 className="text-3xl font-bold text-slate-900 mb-4">
          Simple, Transparent Pricing
        </h3>
        <p className="text-slate-600 text-lg">
          Choose the plan that fits your creator journey
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
        {plans.map((plan, index) => (
          <Card 
            key={index} 
            className={`border-2 hover:shadow-lg transition-shadow relative ${
              plan.popular ? 'border-red-500' : ''
            }`}
          >
            {plan.popular && (
              <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-red-500">
                Most Popular
              </Badge>
            )}
            <CardHeader>
              <CardTitle className="text-center">{plan.name}</CardTitle>
              <div className="text-center">
                <span className="text-3xl font-bold">{plan.price}</span>
                <span className="text-slate-600">/month</span>
              </div>
              <CardDescription className="text-center">{plan.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-sm">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <ArrowUp className="h-4 w-4 text-green-500 mr-2" />
                    {feature}
                  </li>
                ))}
              </ul>
              <Button className="w-full mt-6" variant={plan.buttonVariant} asChild>
                <Link to="/auth">{plan.buttonText}</Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};

export default PricingSection;
