
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Star } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const PricingSection = () => {
  const plans = [
    {
      name: "Free",
      price: "$0",
      description: "Perfect for getting started",
      features: [
        "1 YouTube channel",
        "Basic analytics",
        "30-day data retention",
        "Manual refresh"
      ],
      buttonText: "Get Started",
      buttonVariant: "outline" as const,
      popular: false
    },
    {
      name: "Pro",
      price: "$19",
      description: "For serious creators",
      features: [
        "5 YouTube channels",
        "Advanced analytics + trends",
        "12-month data retention",
        "Daily auto-refresh",
        "Email reports"
      ],
      buttonText: "Start Pro Trial",
      buttonVariant: "default" as const,
      popular: true
    },
    {
      name: "Premium",
      price: "$49",
      description: "For agencies & power users",
      features: [
        "10 YouTube channels",
        "All analytics + insights",
        "Unlimited data retention",
        "Real-time refresh",
        "Priority support",
        "API access"
      ],
      buttonText: "Contact Sales",
      buttonVariant: "outline" as const,
      popular: false
    }
  ];

  return (
    <section className="container mx-auto px-4 py-20">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
          Simple, Transparent Pricing
        </h2>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Choose the plan that fits your creator journey. All plans include our core features with no hidden fees.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {plans.map((plan, index) => (
          <Card
            key={index}
            className={`relative hover:professional-shadow-lg transition-all duration-300 border-border/50 bg-card/50 backdrop-blur-sm ${
              plan.popular ? 'ring-2 ring-primary/50 scale-105' : ''
            }`}
          >
            {plan.popular && (
              <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground">
                <Star className="h-3 w-3 mr-1" />
                Most Popular
              </Badge>
            )}
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-2xl font-bold text-foreground">{plan.name}</CardTitle>
              <div className="mt-4">
                <span className="text-5xl font-bold text-foreground">{plan.price}</span>
                <span className="text-muted-foreground text-lg">/month</span>
              </div>
              <CardDescription className="text-muted-foreground mt-2">{plan.description}</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <Check className="h-5 w-5 text-primary mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-foreground">{feature}</span>
                  </li>
                ))}
              </ul>
              <Button
                className={`w-full h-12 text-base font-medium ${plan.popular ? 'professional-shadow' : ''}`}
                variant={plan.buttonVariant}
                asChild
              >
                <Link to="/auth">{plan.buttonText}</Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center mt-12">
        <p className="text-muted-foreground">
          All plans include 14-day free trial • No setup fees • Cancel anytime
        </p>
      </div>
    </section>
  );
};

export default PricingSection;
