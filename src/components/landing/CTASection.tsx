
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const CTASection = () => {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-br from-primary/10 to-accent/10 rounded-3xl p-12 border border-border/50 professional-shadow-lg">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Ready to Grow Your YouTube Channels?
            </h2>
            <p className="text-xl text-muted-foreground mb-10 max-w-2xl mx-auto leading-relaxed">
              Join thousands of creators using ChannelScope to track, analyze, and optimize their content for maximum growth
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button size="lg" className="text-lg px-10 py-6 h-auto professional-shadow" asChild>
                <Link to="/auth">Start Your Free Trial</Link>
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-10 py-6 h-auto" asChild>
                <Link to="/demo">Explore Demo</Link>
              </Button>
            </div>
            <div className="mt-8 text-sm text-muted-foreground">
              <p>✨ 14-day free trial • No credit card required • Setup in 2 minutes</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
