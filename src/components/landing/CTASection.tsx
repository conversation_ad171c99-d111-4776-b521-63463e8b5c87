
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const CTASection = () => {
  return (
    <section className="bg-slate-900 text-white py-16">
      <div className="container mx-auto px-4 text-center">
        <h3 className="text-3xl font-bold mb-4">
          Ready to Grow Your YouTube Channels?
        </h3>
        <p className="text-xl text-slate-300 mb-8">
          Join thousands of creators using ChannelScope to track and optimize their content
        </p>
        <Button size="lg" className="text-lg px-8 bg-red-500 hover:bg-red-600" asChild>
          <Link to="/auth">Start Your Free Trial</Link>
        </Button>
      </div>
    </section>
  );
};

export default CTASection;
