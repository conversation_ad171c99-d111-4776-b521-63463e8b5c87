
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { Link } from "react-router-dom";

const HeroSection = () => {
  const { user } = useAuth();

  return (
    <section className="container mx-auto px-4 py-20 text-center">
      <div className="max-w-4xl mx-auto">
        <Badge variant="secondary" className="mb-6 px-4 py-2 text-sm font-medium">
          Multi-channel YouTube Analytics Platform
        </Badge>
        <h1 className="text-5xl md:text-7xl font-bold text-foreground mb-8 leading-tight">
          Track Your YouTube Success
          <span className="text-primary block mt-2"> Across All Channels</span>
        </h1>
        <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
          Professional analytics for content creators. Monitor multiple YouTube channels,
          track growth patterns, and make data-driven decisions to scale your audience.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          {user ? (
            <Button size="lg" className="text-lg px-10 py-6 h-auto professional-shadow" asChild>
              <Link to="/dashboard">Go to Dashboard</Link>
            </Button>
          ) : (
            <>
              <Button size="lg" className="text-lg px-10 py-6 h-auto professional-shadow" asChild>
                <Link to="/auth">Start Free Trial</Link>
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-10 py-6 h-auto" asChild>
                <Link to="/demo">View Live Demo</Link>
              </Button>
            </>
          )}
        </div>
        <div className="mt-16 text-sm text-muted-foreground">
          <p>✨ No credit card required • 14-day free trial • Cancel anytime</p>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
