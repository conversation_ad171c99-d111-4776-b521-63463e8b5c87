
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { Link } from "react-router-dom";

const HeroSection = () => {
  const { user } = useAuth();

  return (
    <section className="container mx-auto px-4 py-16 text-center">
      <Badge variant="secondary" className="mb-4">
        Multi-channel YouTube Analytics
      </Badge>
      <h2 className="text-4xl md:text-6xl font-bold text-slate-900 mb-6">
        Track Your YouTube Success
        <span className="text-red-500"> Across All Channels</span>
      </h2>
      <p className="text-xl text-slate-600 mb-8 max-w-2xl mx-auto">
        Simple, powerful analytics for content creators. Monitor multiple YouTube channels, 
        track growth, and make data-driven decisions to grow your audience.
      </p>
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        {user ? (
          <Button size="lg" className="text-lg px-8" asChild>
            <Link to="/dashboard">Go to Dashboard</Link>
          </Button>
        ) : (
          <>
            <Button size="lg" className="text-lg px-8" asChild>
              <Link to="/auth">Start Free Trial</Link>
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8" asChild>
              <Link to="/demo">View Demo</Link>
            </Button>
          </>
        )}
      </div>
    </section>
  );
};

export default HeroSection;
