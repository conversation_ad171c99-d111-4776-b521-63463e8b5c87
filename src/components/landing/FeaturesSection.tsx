
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowUp } from "lucide-react";

const FeaturesSection = () => {
  const features = [
    {
      title: "Multi-Channel Tracking",
      description: "Connect and monitor up to 10 YouTube channels in one dashboard",
      color: "bg-blue-100",
      iconColor: "text-blue-600"
    },
    {
      title: "Real-Time Analytics",
      description: "Get live subscriber counts, views, and engagement metrics",
      color: "bg-green-100",
      iconColor: "text-green-600"
    },
    {
      title: "Growth Insights",
      description: "Understand trends and patterns to optimize your content strategy",
      color: "bg-purple-100",
      iconColor: "text-purple-600"
    }
  ];

  return (
    <section className="container mx-auto px-4 py-16">
      <div className="text-center mb-12">
        <h3 className="text-3xl font-bold text-slate-900 mb-4">
          Everything You Need to Grow
        </h3>
        <p className="text-slate-600 text-lg">
          Built for creators who manage multiple channels
        </p>
      </div>
      
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {features.map((feature, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className={`${feature.color} w-12 h-12 rounded-lg flex items-center justify-center mb-4`}>
                <ArrowUp className={`h-6 w-6 ${feature.iconColor}`} />
              </div>
              <CardTitle>{feature.title}</CardTitle>
              <CardDescription>
                {feature.description}
              </CardDescription>
            </CardHeader>
          </Card>
        ))}
      </div>
    </section>
  );
};

export default FeaturesSection;
