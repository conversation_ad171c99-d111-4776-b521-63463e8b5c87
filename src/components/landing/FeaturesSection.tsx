
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3, Zap, TrendingUp, Shield, Users, Eye } from "lucide-react";

const FeaturesSection = () => {
  const features = [
    {
      title: "Multi-Channel Tracking",
      description: "Connect and monitor up to 10 YouTube channels in one unified dashboard with seamless integration",
      icon: BarChart3,
      gradient: "from-blue-500/20 to-cyan-500/20"
    },
    {
      title: "Real-Time Analytics",
      description: "Get live subscriber counts, views, and engagement metrics updated every minute",
      icon: Zap,
      gradient: "from-emerald-500/20 to-green-500/20"
    },
    {
      title: "Growth Insights",
      description: "Understand trends and patterns with AI-powered insights to optimize your content strategy",
      icon: TrendingUp,
      gradient: "from-purple-500/20 to-pink-500/20"
    },
    {
      title: "Secure & Private",
      description: "Enterprise-grade security with encrypted data and privacy-first approach to your analytics",
      icon: Shield,
      gradient: "from-orange-500/20 to-red-500/20"
    },
    {
      title: "Audience Analytics",
      description: "Deep dive into your audience demographics, behavior patterns, and engagement metrics",
      icon: Users,
      gradient: "from-indigo-500/20 to-blue-500/20"
    },
    {
      title: "Performance Tracking",
      description: "Monitor video performance, track viral content, and identify your best-performing uploads",
      icon: Eye,
      gradient: "from-teal-500/20 to-cyan-500/20"
    }
  ];

  return (
    <section className="container mx-auto px-4 py-20">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
          Everything You Need to Grow
        </h2>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Professional-grade tools built for creators who manage multiple channels and demand excellence
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
        {features.map((feature, index) => {
          const IconComponent = feature.icon;
          return (
            <Card key={index} className="group hover:professional-shadow-lg transition-all duration-300 border-border/50 bg-card/50 backdrop-blur-sm">
              <CardHeader className="p-8">
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <IconComponent className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-xl font-semibold text-foreground mb-3">{feature.title}</CardTitle>
                <CardDescription className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardHeader>
            </Card>
          );
        })}
      </div>
    </section>
  );
};

export default FeaturesSection;
