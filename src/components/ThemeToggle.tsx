import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';

const ThemeToggle: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      className="h-9 w-9 p-0 hover:bg-accent/50 transition-colors"
      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
    >
      {theme === 'dark' ? (
        <Sun className="h-4 w-4 text-foreground/70 hover:text-foreground transition-colors" />
      ) : (
        <Moon className="h-4 w-4 text-foreground/70 hover:text-foreground transition-colors" />
      )}
    </Button>
  );
};

export default ThemeToggle;
