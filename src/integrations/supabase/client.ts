
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://cxwvccicqldgyluawdtv.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN4d3ZjY2ljcWxkZ3lsdWF3ZHR2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3NjYzMDcsImV4cCI6MjA2NDM0MjMwN30.atVNyzK6h5nn0ckZZylApJkvQsiiV5aGf0u4yV0VQJI";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
