
import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  AuthUser,
  AuthSession,
  authenticateUser,
  createUser,
  createSession,
  getSessionByToken,
  deleteSession,
  createOrUpdateOAuthAccount,
  findUserByOAuthAccount,
  findUserByEmail
} from '@/lib/auth/utils';
import { getGoogleAuthUrl, exchangeCodeForTokens, getGoogleUserInfo } from '@/lib/auth/google';

interface AuthContextType {
  user: AuthUser | null;
  session: AuthSession | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<AuthSession | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing session on mount
    const checkSession = async () => {
      try {
        const sessionToken = localStorage.getItem('sessionToken');
        if (sessionToken) {
          const session = await getSessionByToken(sessionToken);
          if (session) {
            setSession(session);
            setUser(session.user);
          } else {
            localStorage.removeItem('sessionToken');
          }
        }
      } catch (error) {
        console.error('Error checking session:', error);
        localStorage.removeItem('sessionToken');
      } finally {
        setLoading(false);
      }
    };

    checkSession();
  }, []);

  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      // Check if user already exists
      const existingUser = await findUserByEmail(email);
      if (existingUser) {
        return { error: { message: 'User already exists with this email' } };
      }

      // Create new user
      const user = await createUser(email, password, fullName);

      // Create session
      const session = await createSession(user.id);

      // Store session token
      localStorage.setItem('sessionToken', session.sessionToken);

      // Update state
      setUser(session.user);
      setSession(session);

      return { error: null };
    } catch (error: any) {
      console.error('Sign up error:', error);
      return { error: { message: error.message || 'Failed to create account' } };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      // Authenticate user
      const user = await authenticateUser(email, password);

      if (!user) {
        return { error: { message: 'Invalid email or password' } };
      }

      // Create session
      const session = await createSession(user.id);

      // Store session token
      localStorage.setItem('sessionToken', session.sessionToken);

      // Update state
      setUser(session.user);
      setSession(session);

      return { error: null };
    } catch (error: any) {
      console.error('Sign in error:', error);
      return { error: { message: error.message || 'Failed to sign in' } };
    }
  };

  const signOut = async () => {
    try {
      const sessionToken = localStorage.getItem('sessionToken');
      if (sessionToken) {
        await deleteSession(sessionToken);
        localStorage.removeItem('sessionToken');
      }

      setUser(null);
      setSession(null);
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const signInWithGoogle = async () => {
    try {
      // Redirect to Google OAuth
      const authUrl = getGoogleAuthUrl();
      window.location.href = authUrl;
      return { error: null };
    } catch (error: any) {
      console.error('Google sign in error:', error);
      return { error: { message: error.message || 'Failed to sign in with Google' } };
    }
  };

  // Handle Google OAuth callback
  const handleGoogleCallback = async (code: string) => {
    try {
      // Exchange code for tokens
      const tokens = await exchangeCodeForTokens(code);

      // Get user info from Google
      const googleUser = await getGoogleUserInfo(tokens.access_token);

      // Check if user exists
      let user = await findUserByOAuthAccount('google', googleUser.id);

      if (!user) {
        // Check if user exists with same email
        user = await findUserByEmail(googleUser.email);

        if (!user) {
          // Create new user
          user = await createUser(googleUser.email, '', googleUser.name);
        }

        // Link OAuth account
        await createOrUpdateOAuthAccount(
          user.id,
          'google',
          googleUser.id,
          tokens.access_token,
          tokens.refresh_token,
          tokens.expires_in ? Math.floor(Date.now() / 1000) + tokens.expires_in : undefined
        );
      } else {
        // Update existing OAuth account
        await createOrUpdateOAuthAccount(
          user.id,
          'google',
          googleUser.id,
          tokens.access_token,
          tokens.refresh_token,
          tokens.expires_in ? Math.floor(Date.now() / 1000) + tokens.expires_in : undefined
        );
      }

      // Create session
      const session = await createSession(user.id);

      // Store session token
      localStorage.setItem('sessionToken', session.sessionToken);

      // Update state
      setUser(session.user);
      setSession(session);

      return { error: null };
    } catch (error: any) {
      console.error('Google callback error:', error);
      return { error: { message: error.message || 'Failed to complete Google sign in' } };
    }
  };

  // Expose handleGoogleCallback for use in callback route
  React.useEffect(() => {
    (window as any).handleGoogleCallback = handleGoogleCallback;
  }, []);

  const value = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    signInWithGoogle
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
